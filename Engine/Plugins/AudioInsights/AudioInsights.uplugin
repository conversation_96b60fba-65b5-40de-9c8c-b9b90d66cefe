{"FileVersion": 3, "Version": 1, "VersionName": "1.0", "FriendlyName": "AudioInsights", "Description": "Suite of tools to profile, debug, and monitor aspects of audio in the Unreal Engine.", "Category": "Audio", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "CanContainContent": true, "IsBetaVersion": false, "IsExperimentalVersion": true, "Installed": false, "SupportedPrograms": ["UnrealInsights"], "Modules": [{"Name": "AudioInsights", "Type": "EditorAndProgram", "LoadingPhase": "<PERSON><PERSON><PERSON>", "ProgramAllowList": ["UnrealInsights"], "PlatformAllowList": ["Win64", "<PERSON>"]}, {"Name": "AudioInsightsEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64", "<PERSON>"]}], "Plugins": [{"Name": "AudioWidgets", "Enabled": true}]}