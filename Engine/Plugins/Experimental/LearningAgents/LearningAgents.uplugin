{"FileVersion": 3, "Version": 2, "VersionName": "0.2", "FriendlyName": "Learning Agents", "Description": "Learning Agents is a machine learning library for AI character control in games. It simplifies the use of reinforcement and imitation learning in Unreal.", "Category": "AI", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "EnabledByDefault": false, "CanContainContent": true, "IsBetaVersion": false, "IsExperimentalVersion": true, "Installed": false, "Modules": [{"Name": "Learning", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "LearningTraining", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "LearningAgents", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "LearningAgentsTraining", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}], "Plugins": [{"Name": "PythonFoundationPackages", "Enabled": true}, {"Name": "NNERuntimeBasicCpu", "Enabled": true}]}