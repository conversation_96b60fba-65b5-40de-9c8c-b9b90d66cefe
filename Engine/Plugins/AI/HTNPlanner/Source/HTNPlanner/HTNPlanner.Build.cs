// Copyright Epic Games, Inc. All Rights Reserved.

namespace UnrealBuildTool.Rules
{
    public class HTNPlanner : ModuleRules
    {
        public HTNPlanner(ReadOnlyTargetRules Target) : base(Target)
        {
			StaticAnalyzerDisabledCheckers.Add("cplusplus.PlacementNew");

			PublicDependencyModuleNames.AddRange(
                new string[] {
                        "Core",
                        "CoreUObject",
                        "Engine",
                        "GameplayTags",
                        "GameplayTasks",
                        "AIModule"
                }
                );

            if (Target.bBuildEditor == true)
            {
                PrivateDependencyModuleNames.Add("EditorFramework");
                PrivateDependencyModuleNames.Add("UnrealEd");
            }

            SetupGameplayDebuggerSupport(Target);
        }
    }
}
