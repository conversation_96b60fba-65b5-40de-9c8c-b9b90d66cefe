{"FileVersion": 3, "Version": 1, "VersionName": "1.0", "FriendlyName": "ConcertInsightsCore", "Description": "Shared logic for starting synchronized tracing", "Category": "Networking", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "", "DocsURL": "", "MarketplaceURL": "", "CanContainContent": false, "Hidden": true, "IsBetaVersion": false, "IsExperimentalVersion": true, "Installed": false, "SupportedPrograms": ["UnrealInsights", "UnrealMultiUserServer", "UnrealMultiUserSlateServer"], "Modules": [{"Name": "ConcertInsightsCore", "Type": "EditorAndProgram", "LoadingPhase": "<PERSON><PERSON><PERSON>", "ProgramAllowList": ["UnrealMultiUserServer", "UnrealMultiUserSlateServer"]}], "Plugins": [{"Name": "ConcertMain", "Enabled": true}]}